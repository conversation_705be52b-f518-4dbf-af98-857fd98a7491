import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Dalti Provider App Theme Configuration
/// Implements Material Design 3 with custom Dalti branding
class AppTheme {
  // Dalti Brand Colors
  static const Color primaryColor = Color(0xFF15424E); // #15424E
  static const Color primaryVariant = Color(0xFF0D3339);
  static const Color secondaryColor = Color(0xFF4ECDC4);
  static const Color accentColor = Color(0xFFFFE66D);

  // Neutral Colors
  static const Color surfaceColor = Color(0xFFFAFAFA);
  static const Color backgroundColor = Color(0xFFFFFFFF);
  static const Color errorColor = Color(0xFFE74C3C);
  static const Color successColor = Color(0xFF27AE60);
  static const Color warningColor = Color(0xFFF39C12);

  // Text Colors
  static const Color textPrimary = Color(0xFF2C3E50);
  static const Color textSecondary = Color(0xFF7F8C8D);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  /// Light Theme Configuration
  static ThemeData get lightTheme {
    final ColorScheme colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: Brightness.light,
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      surface: surfaceColor,
      background: backgroundColor,
      error: errorColor,
      onPrimary: textOnPrimary,
      onSecondary: textPrimary,
      onSurface: textPrimary,
      onBackground: textPrimary,
      onError: textOnPrimary,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        foregroundColor: textPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.changa(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        iconTheme: const IconThemeData(color: textPrimary),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: backgroundColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondary,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Bottom App Bar Theme - For FAB integration
      bottomAppBarTheme: const BottomAppBarTheme(
        elevation: 0,
        shape: CircularNotchedRectangle(),
      ),

      // Drawer Theme
      drawerTheme: DrawerThemeData(
        backgroundColor: backgroundColor,
        surfaceTintColor: surfaceColor,
        elevation: 16,
      ),

      // Card Theme
      cardTheme: CardThemeData(
        color: backgroundColor,
        surfaceTintColor: surfaceColor,
        elevation: 1,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: textOnPrimary,
          elevation: 2,
          shadowColor: primaryColor.withOpacity(0.3),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: GoogleFonts.changa(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: BorderSide(color: primaryColor, width: 1.5),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: GoogleFonts.changa(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: GoogleFonts.changa(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: textOnPrimary,
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: textSecondary.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: textSecondary.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: errorColor, width: 1.5),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        labelStyle: GoogleFonts.changa(color: textSecondary),
        hintStyle: GoogleFonts.changa(color: textSecondary.withOpacity(0.7)),
      ),

      // Tab Bar Theme
      tabBarTheme: TabBarThemeData(
        labelColor: textOnPrimary,
        unselectedLabelColor: textOnPrimary.withOpacity(0.7),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: accentColor, width: 3),
        ),
        labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: surfaceColor,
        selectedColor: primaryColor.withOpacity(0.2),
        labelStyle: const TextStyle(color: textPrimary),
        secondaryLabelStyle: const TextStyle(color: textOnPrimary),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),

      // SnackBar Theme - Ensure high elevation above FAB
      snackBarTheme: const SnackBarThemeData(
        elevation: 12.0, // High elevation to appear above FAB
        behavior: SnackBarBehavior.floating,
      ),

      // Typography with Changa font
      textTheme: GoogleFonts.changaTextTheme().copyWith(
        displayLarge: GoogleFonts.changa(
          fontSize: 32,
          fontWeight: FontWeight.w700,
          color: textPrimary,
        ),
        displayMedium: GoogleFonts.changa(
          fontSize: 28,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        displaySmall: GoogleFonts.changa(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        headlineLarge: GoogleFonts.changa(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        headlineMedium: GoogleFonts.changa(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        headlineSmall: GoogleFonts.changa(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        titleLarge: GoogleFonts.changa(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        titleMedium: GoogleFonts.changa(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        titleSmall: GoogleFonts.changa(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        bodyLarge: GoogleFonts.changa(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: textPrimary,
        ),
        bodyMedium: GoogleFonts.changa(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: textPrimary,
        ),
        bodySmall: GoogleFonts.changa(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: textSecondary,
        ),
        labelLarge: GoogleFonts.changa(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        labelMedium: GoogleFonts.changa(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        labelSmall: GoogleFonts.changa(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: textSecondary,
        ),
      ),
    );
  }

  // Dark Theme Colors - Extracted from medical professional profile interface
  static const Color darkBackground = Color(
    0xFF0F1419,
  ); // Very dark blue-gray background
  static const Color darkSurface = Color(
    0xFF1C2127,
  ); // Slightly lighter dark surface for cards
  static const Color darkSurfaceVariant = Color(
    0xFF242A30,
  ); // Elevated elements and sections
  static const Color darkSurfaceContainer = Color(
    0xFF2A3038,
  ); // Container backgrounds (progress bars, etc.)
  static const Color darkSurfaceContainerHigh = Color(
    0xFF323A42,
  ); // High emphasis containers

  // Dark theme primary colors - Teal/Cyan from the interface
  static const Color darkPrimary = Color(
    0xFF15424E,
  ); // Primary teal for buttons and highlights
  static const Color darkPrimaryContainer = Color(
    0xFF1A3A42,
  ); // Dark container for primary elements
  static const Color darkSecondary = Color(
    0xFF3ABAB3,
  ); // Darker variant of teal
  static const Color darkTertiary = Color(
    0xFF15424E,
  ); // Using same teal for consistency

  // Dark theme text colors - WCAG AA compliant from interface
  static const Color darkOnSurface = Color(
    0xFFF8F9FA,
  ); // Primary white text for names, headings
  static const Color darkOnSurfaceVariant = Color(
    0xFFB8BCC8,
  ); // Secondary gray text for descriptions
  static const Color darkOnSurfaceSecondary = Color(
    0xFF6C7278,
  ); // Tertiary gray for timestamps, secondary info
  static const Color darkOnPrimary = Color(
    0xFFFFFFFF,
  ); // White text on teal buttons
  static const Color darkOnPrimaryContainer = Color(
    0xFFB8F5FF,
  ); // Light text on primary container

  // Additional interface-specific colors
  static const Color darkInactiveElement = Color(
    0xFF3A4048,
  ); // For inactive stars, disabled elements
  static const Color darkProgressBackground = Color(
    0xFF2A3038,
  ); // Progress bar backgrounds

  /// Dark Theme Configuration - Based on extracted medical professional interface colors
  static ThemeData get darkTheme {
    final ColorScheme colorScheme = ColorScheme.dark(
      // Primary colors - Teal from interface
      primary: darkPrimary, // #4ECDC4
      onPrimary: darkOnPrimary, // White text on teal
      primaryContainer: darkPrimaryContainer,
      onPrimaryContainer: darkOnPrimaryContainer,

      // Secondary colors - Darker teal variant
      secondary: darkSecondary, // #3ABAB3
      onSecondary: const Color(0xFFFFFFFF), // White on secondary
      secondaryContainer: const Color(0xFF1A4A52),
      onSecondaryContainer: const Color(0xFFB8F5FF),

      // Tertiary colors - Using teal for consistency
      tertiary: darkTertiary, // #4ECDC4
      onTertiary: const Color(0xFFFFFFFF), // White on tertiary
      tertiaryContainer: const Color(0xFF1A4A52),
      onTertiaryContainer: const Color(0xFFB8F5FF),

      // Error colors - Keeping accessible error colors
      error: const Color(0xFFFFB4AB),
      onError: const Color(0xFF690005),
      errorContainer: const Color(0xFF93000A),
      onErrorContainer: const Color(0xFFFFDAD6),

      // Surface colors - From extracted interface
      surface: darkSurface, // #1C2127
      onSurface: darkOnSurface, // #F8F9FA
      surfaceVariant: darkSurfaceVariant, // #242A30
      onSurfaceVariant: darkOnSurfaceVariant, // #B8BCC8
      surfaceContainerLowest: darkBackground, // #0F1419
      surfaceContainerLow: const Color(0xFF1A1F24),
      surfaceContainer: darkSurfaceContainer, // #2A3038
      surfaceContainerHigh: darkSurfaceContainerHigh, // #323A42
      surfaceContainerHighest: const Color(0xFF3A4048),

      // Background
      background: darkBackground, // #0F1419
      onBackground: darkOnSurface, // #F8F9FA
      // Outline colors - Adjusted for interface consistency
      outline: darkOnSurfaceSecondary, // #6C7278
      outlineVariant: darkInactiveElement, // #3A4048
      // Other colors
      shadow: const Color(0xFF000000),
      scrim: const Color(0xFF000000),
      inverseSurface: const Color(0xFFF8F9FA),
      onInverseSurface: const Color(0xFF0F1419),
      inversePrimary: primaryColor,
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      scaffoldBackgroundColor: darkBackground,

      // App Bar Theme - Dark optimized
      appBarTheme: AppBarTheme(
        backgroundColor: darkSurface,
        surfaceTintColor: darkSurface,
        foregroundColor: darkOnSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: GoogleFonts.changa(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        iconTheme: IconThemeData(color: darkOnSurface),
        actionsIconTheme: IconThemeData(color: darkOnSurface),
      ),

      // Bottom Navigation Bar Theme - Dark optimized
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: darkSurface,
        selectedItemColor: darkPrimary,
        unselectedItemColor: darkOnSurfaceVariant,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Bottom App Bar Theme - For FAB integration
      bottomAppBarTheme: const BottomAppBarTheme(
        elevation: 0,
        shape: CircularNotchedRectangle(),
      ),

      // Drawer Theme - Dark optimized
      drawerTheme: DrawerThemeData(
        backgroundColor: darkSurface,
        surfaceTintColor: darkPrimary,
        elevation: 16,
        shadowColor: Colors.black.withOpacity(0.3),
      ),

      // Card Theme - Optimized for extracted interface colors
      cardTheme: CardThemeData(
        color:
            darkSurfaceVariant, // #242A30 - matches interface card backgrounds
        surfaceTintColor: darkPrimary, // Subtle teal tint
        elevation: 1,
        shadowColor: Colors.black.withOpacity(0.3), // Softer shadow
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      ),

      // Elevated Button Theme - Dark optimized
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: darkPrimary,
          foregroundColor: darkOnPrimary,
          elevation: 2,
          shadowColor: darkPrimary.withOpacity(0.3),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: GoogleFonts.changa(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Outlined Button Theme - Dark optimized
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: darkPrimary,
          side: BorderSide(color: darkPrimary, width: 1.5),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: GoogleFonts.changa(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Text Button Theme - Dark optimized
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: darkPrimary,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: GoogleFonts.changa(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // Floating Action Button Theme - Dark optimized
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: darkPrimary,
        foregroundColor: darkOnPrimary,
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),

      // Input Decoration Theme - Dark optimized
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSurfaceContainer,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: darkOnSurfaceVariant.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: darkOnSurfaceVariant.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: darkPrimary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.error, width: 1.5),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        labelStyle: GoogleFonts.changa(color: darkOnSurfaceVariant),
        hintStyle: GoogleFonts.changa(
          color: darkOnSurfaceVariant.withOpacity(0.7),
        ),
      ),

      // Tab Bar Theme - Dark optimized
      tabBarTheme: TabBarThemeData(
        labelColor: darkOnSurface,
        unselectedLabelColor: darkOnSurfaceVariant,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: darkTertiary, width: 3),
        ),
        labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Chip Theme - Matching interface button styling
      chipTheme: ChipThemeData(
        backgroundColor: darkSurfaceContainer, // #2A3038 - container background
        selectedColor: darkPrimary, // #4ECDC4 - teal for selected chips
        disabledColor: darkInactiveElement, // #3A4048 - for disabled chips
        labelStyle: TextStyle(color: darkOnSurface), // #F8F9FA - primary text
        secondaryLabelStyle: TextStyle(
          color: darkOnPrimary,
        ), // White on selected
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),

      // SnackBar Theme - Ensure high elevation above FAB
      snackBarTheme: const SnackBarThemeData(
        elevation: 12.0, // High elevation to appear above FAB
        behavior: SnackBarBehavior.floating,
      ),

      // List Tile Theme - Optimized for interface consistency
      listTileTheme: ListTileThemeData(
        tileColor: Colors.transparent,
        selectedTileColor: darkPrimaryContainer.withOpacity(0.2),
        iconColor:
            darkOnSurfaceVariant, // #B8BCC8 - matches interface secondary text
        textColor: darkOnSurface, // #F8F9FA - matches interface primary text
        selectedColor: darkPrimary, // #4ECDC4 - teal highlight
        subtitleTextStyle: TextStyle(
          color:
              darkOnSurfaceSecondary, // #6C7278 - for timestamps and secondary info
          fontSize: 14,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),

      // Divider Theme - Subtle dividers matching interface
      dividerTheme: DividerThemeData(
        color: darkOnSurfaceSecondary.withOpacity(0.3), // #6C7278 with opacity
        thickness: 1,
        space: 1,
      ),

      // Icon Theme - Matching interface icon colors
      iconTheme: IconThemeData(
        color: darkOnSurfaceVariant, // #B8BCC8 - secondary text color
        size: 24,
      ),

      // Primary Icon Theme - Teal icons for emphasis
      primaryIconTheme: IconThemeData(
        color: darkPrimary, // #4ECDC4 - teal for primary icons
        size: 24,
      ),

      // Progress Indicator Theme - Matching interface progress bars
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: darkPrimary, // #4ECDC4 - teal progress
        linearTrackColor: darkProgressBackground, // #2A3038 - dark track
        circularTrackColor: darkProgressBackground,
      ),

      // Typography - Dark theme optimized with Changa font
      textTheme: GoogleFonts.changaTextTheme().copyWith(
        displayLarge: GoogleFonts.changa(
          fontSize: 32,
          fontWeight: FontWeight.w700,
          color: darkOnSurface,
        ),
        displayMedium: GoogleFonts.changa(
          fontSize: 28,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        displaySmall: GoogleFonts.changa(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        headlineLarge: GoogleFonts.changa(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        headlineMedium: GoogleFonts.changa(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        headlineSmall: GoogleFonts.changa(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        titleLarge: GoogleFonts.changa(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        titleMedium: GoogleFonts.changa(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        titleSmall: GoogleFonts.changa(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        bodyLarge: GoogleFonts.changa(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: darkOnSurface,
        ),
        bodyMedium: GoogleFonts.changa(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: darkOnSurface,
        ),
        bodySmall: GoogleFonts.changa(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: darkOnSurfaceVariant,
        ),
        labelLarge: GoogleFonts.changa(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        labelMedium: GoogleFonts.changa(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: darkOnSurface,
        ),
        labelSmall: GoogleFonts.changa(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: darkOnSurfaceVariant,
        ),
      ),

      // Additional Material 3 component themes
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: darkSurface,
        indicatorColor: darkPrimaryContainer,
        labelTextStyle: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: darkPrimary,
            );
          }
          return TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: darkOnSurfaceVariant,
          );
        }),
        iconTheme: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return IconThemeData(color: darkPrimary);
          }
          return IconThemeData(color: darkOnSurfaceVariant);
        }),
      ),

      // Switch Theme - Dark optimized
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return darkPrimary;
          }
          return darkOnSurfaceVariant;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return darkPrimaryContainer;
          }
          return darkSurfaceContainer;
        }),
      ),

      // Checkbox Theme - Dark optimized
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return darkPrimary;
          }
          return Colors.transparent;
        }),
        checkColor: MaterialStateProperty.all(darkOnPrimary),
        side: BorderSide(color: darkOnSurfaceVariant, width: 2),
      ),

      // Radio Theme - Dark optimized
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return darkPrimary;
          }
          return darkOnSurfaceVariant;
        }),
      ),
    );
  }
}
